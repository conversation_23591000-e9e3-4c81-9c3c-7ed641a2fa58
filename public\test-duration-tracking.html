<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Duration Tracking Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #005a87;
        }
        .test-results {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            margin: 5px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Duration Tracking Test</h1>
    <p>This page tests the duration tracking functionality for the user journey system.</p>

    <div class="test-section">
        <h2>Test Controls</h2>
        <button class="test-button" onclick="testFeatureAccess()">Test Feature Access</button>
        <button class="test-button" onclick="testFeatureUsage()">Test Feature Usage</button>
        <button class="test-button" onclick="testFeatureTransition()">Test Feature Transition</button>
        <button class="test-button" onclick="testDurationFormatting()">Test Duration Formatting</button>
        <button class="test-button" onclick="clearResults()">Clear Results</button>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="test-results" class="test-results">Ready to run tests...</div>
    </div>

    <div class="test-section">
        <h2>Active Sessions</h2>
        <div id="active-sessions" class="test-results">No active sessions</div>
        <button class="test-button" onclick="updateActiveSessions()">Refresh Active Sessions</button>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-firestore.js"></script>

    <!-- User Journey Tracker -->
    <script src="user-journey-tracker.js"></script>

    <script>
        let testResults = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testResults.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateResults();
        }

        function updateResults() {
            document.getElementById('test-results').textContent = testResults.join('\n');
        }

        function clearResults() {
            testResults = [];
            updateResults();
        }

        function updateActiveSessions() {
            if (window.UserJourneyTracker && window.UserJourneyTracker.getActiveFeatureSessions) {
                const sessions = window.UserJourneyTracker.getActiveFeatureSessions();
                const sessionInfo = [];
                
                for (const [key, session] of sessions) {
                    const duration = new Date() - session.startTime;
                    sessionInfo.push(`${key}: ${Math.round(duration/1000)}s`);
                }
                
                document.getElementById('active-sessions').textContent = 
                    sessionInfo.length > 0 ? sessionInfo.join('\n') : 'No active sessions';
            } else {
                document.getElementById('active-sessions').textContent = 'UserJourneyTracker not available';
            }
        }

        function testFeatureAccess() {
            log('Testing feature access tracking...');
            
            if (!window.UserJourneyTracker) {
                log('UserJourneyTracker not available', 'error');
                return;
            }

            try {
                // Test accessing dashboard feature
                window.UserJourneyTracker.trackFeatureAccess('dashboard', {
                    test: true,
                    source: 'duration-test'
                });
                log('Feature access tracked successfully', 'success');
                
                // Wait a moment then update active sessions
                setTimeout(updateActiveSessions, 100);
            } catch (error) {
                log(`Error tracking feature access: ${error.message}`, 'error');
            }
        }

        function testFeatureUsage() {
            log('Testing feature usage tracking...');
            
            if (!window.UserJourneyTracker) {
                log('UserJourneyTracker not available', 'error');
                return;
            }

            try {
                // Test using assessments feature
                window.UserJourneyTracker.trackFeatureUsage('assessments', {
                    usageType: 'used',
                    test: true,
                    source: 'duration-test'
                });
                log('Feature usage tracked successfully', 'success');
                
                // Wait a moment then update active sessions
                setTimeout(updateActiveSessions, 100);
            } catch (error) {
                log(`Error tracking feature usage: ${error.message}`, 'error');
            }
        }

        function testFeatureTransition() {
            log('Testing feature transition...');
            
            if (!window.UserJourneyTracker) {
                log('UserJourneyTracker not available', 'error');
                return;
            }

            try {
                // Start with dashboard access
                window.UserJourneyTracker.trackFeatureAccess('dashboard');
                log('Started dashboard session');
                
                // Wait 2 seconds then transition to reports
                setTimeout(() => {
                    window.UserJourneyTracker.trackFeatureAccess('reports');
                    log('Transitioned to reports');
                    updateActiveSessions();
                }, 2000);
                
                updateActiveSessions();
            } catch (error) {
                log(`Error testing feature transition: ${error.message}`, 'error');
            }
        }

        function testDurationFormatting() {
            log('Testing duration formatting...');
            
            // Test various duration values
            const testDurations = [
                1000,      // 1 second
                65000,     // 1 minute 5 seconds
                3661000,   // 1 hour 1 minute 1 second
                90061000   // 1 day 1 hour 1 minute 1 second
            ];
            
            testDurations.forEach(duration => {
                // We'll simulate the formatting function here since it's in the dashboard file
                const formatted = formatDurationTest(duration);
                log(`${duration}ms = ${formatted}`);
            });
        }

        // Simple duration formatting for testing
        function formatDurationTest(milliseconds) {
            if (!milliseconds || milliseconds < 0) return '0s';
            
            const seconds = Math.floor(milliseconds / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);
            
            if (days > 0) {
                const remainingHours = hours % 24;
                return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days}d`;
            } else if (hours > 0) {
                const remainingMinutes = minutes % 60;
                return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
            } else if (minutes > 0) {
                const remainingSeconds = seconds % 60;
                return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
            } else {
                return `${seconds}s`;
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('Duration tracking test page loaded');
            
            // Try to initialize the tracker
            if (window.UserJourneyTracker && window.UserJourneyTracker.initialize) {
                try {
                    window.UserJourneyTracker.initialize();
                    log('UserJourneyTracker initialized', 'success');
                } catch (error) {
                    log(`Failed to initialize UserJourneyTracker: ${error.message}`, 'error');
                }
            } else {
                log('UserJourneyTracker not available', 'error');
            }
            
            // Update active sessions every 5 seconds
            setInterval(updateActiveSessions, 5000);
        });
    </script>
</body>
</html>
