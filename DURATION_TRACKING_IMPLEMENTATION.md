# Duration Tracking Implementation

This document describes the implementation of duration tracking for the user journey modal in the super admin dashboard.

## Overview

The duration tracking system measures how long users spend on each feature, distinguishing between:
- **Accessed**: Time spent after navigating to a feature (passive engagement)
- **Used**: Time spent during meaningful interaction with a feature (active engagement)

## Implementation Details

### 1. Data Structure Updates

Each feature in the `userJourney.features` object now includes:

```javascript
{
  // Existing fields
  accessed: boolean,
  used: boolean,
  firstAccessed: timestamp,
  firstUsed: timestamp,
  lastAccess: timestamp,
  lastUsed: timestamp,
  accessCount: number,
  usageCount: number,
  usageDetails: [],

  // New duration tracking fields
  totalAccessDuration: number,      // Total time spent accessing (ms)
  totalUsageDuration: number,       // Total time spent using (ms)
  averageAccessDuration: number,    // Average access session duration (ms)
  averageUsageDuration: number,     // Average usage session duration (ms)
  accessSessions: [],              // Array of access session records
  usageSessions: []                // Array of usage session records
}
```

### 2. Session Tracking

#### Session Records
Each session record contains:
```javascript
{
  startTime: Date,
  endTime: Date,
  duration: number,    // milliseconds
  sessionId: string,
  timestamp: Date
}
```

#### Active Session Management
- `activeFeatureSessions` Map tracks currently active sessions
- Sessions are automatically ended when:
  - User navigates to a different feature
  - Page becomes hidden (visibility API)
  - User closes/refreshes the page (beforeunload)
  - Feature transitions from 'accessed' to 'used'

### 3. Key Functions

#### Core Duration Tracking
- `startFeatureSession(featureName, sessionType)` - Starts timing a feature session
- `endFeatureSession(featureName, sessionType)` - Ends timing and stores duration
- `endAllActiveFeatureSessions()` - Ends all active sessions
- `storeDurationData()` - Saves duration data to Firestore

#### Integration Points
- `trackFeatureAccess()` - Automatically starts 'accessed' session
- `trackFeatureUsage()` - Transitions from 'accessed' to 'used' session
- `trackNavigation()` - Handles feature transitions

### 4. Modal Display Updates

#### New Statistics
- **Total Engagement**: Sum of all feature usage time (prioritizes usage over access)
- **Feature Cards**: Show duration metrics for each feature:
  - Total Time: Cumulative time spent
  - Avg Session: Average session duration
  - Usage Time: Time spent in meaningful interaction
  - Avg Usage: Average usage session duration

#### Duration Formatting
- `formatDuration(ms)` - Converts milliseconds to human-readable format
- Examples: "2m 30s", "1h 15m", "2d 3h"
- `formatDurationShort(ms)` - Shorter format for compact display

### 5. Usage Examples

#### Basic Feature Access
```javascript
// User navigates to dashboard
UserJourneyTracker.trackFeatureAccess('dashboard');
// Starts 'accessed' session automatically

// User performs meaningful action
UserJourneyTracker.trackFeatureUsage('dashboard', { usageType: 'used' });
// Ends 'accessed' session, starts 'used' session
```

#### Feature Transitions
```javascript
// User navigates from dashboard to assessments
UserJourneyTracker.trackNavigation('dashboard', 'assessments');
// Ends dashboard sessions, starts assessments 'accessed' session
```

### 6. Data Flow

1. **Session Start**: User navigates to feature → `startFeatureSession()` called
2. **Session Active**: Duration accumulates while user remains on feature
3. **Session End**: User leaves feature → `endFeatureSession()` called
4. **Data Storage**: Duration data saved to Firestore via `storeDurationData()`
5. **Modal Display**: Duration statistics calculated and formatted for display

### 7. Performance Considerations

- **Session Limits**: Only last 20 sessions stored per feature/type
- **Event Limits**: Only last 500 events stored in journey
- **Automatic Cleanup**: Old session data automatically pruned
- **Efficient Queries**: Duration calculations done client-side

### 8. Testing

Use `public/test-duration-tracking.html` to test:
- Feature access/usage tracking
- Session transitions
- Duration formatting
- Active session monitoring

### 9. Backward Compatibility

- Existing journey data continues to work
- New duration fields are optional
- Graceful degradation when duration data unavailable
- No breaking changes to existing API

## Benefits

1. **Deeper Insights**: Understand user engagement depth, not just visits
2. **Feature Optimization**: Identify features with low engagement time
3. **User Behavior**: Distinguish between browsing and active usage
4. **Performance Metrics**: Track how long users spend on different tasks
5. **Retention Analysis**: Correlate engagement time with user retention

## Future Enhancements

- Session quality scoring based on duration patterns
- Engagement heatmaps showing peak usage times
- Comparative analytics across user cohorts
- Automated alerts for unusual engagement patterns
